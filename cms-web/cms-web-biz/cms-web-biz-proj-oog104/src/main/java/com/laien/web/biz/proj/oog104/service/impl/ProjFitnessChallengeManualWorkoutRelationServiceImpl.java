package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessChallengeManualWorkoutRelation;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessChallengeManualWorkoutRelationMapper;
import com.laien.web.biz.proj.oog104.service.IProjFitnessChallengeManualWorkoutRelationService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_fitness_challenge_manual_workout_relation Service实现
 * <p>
 *
 * <AUTHOR>
 * @since 2025/08/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjFitnessChallengeManualWorkoutRelationServiceImpl
        extends ServiceImpl<ProjFitnessChallengeManualWorkoutRelationMapper, ProjFitnessChallengeManualWorkoutRelation>
        implements IProjFitnessChallengeManualWorkoutRelationService {

    @Override
    public List<Integer> getWorkoutIdsByChallengeId(Integer challengeId) {
        if (challengeId == null) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjFitnessChallengeManualWorkoutRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ProjFitnessChallengeManualWorkoutRelation::getProjFitnessManualWorkoutId)
                .eq(ProjFitnessChallengeManualWorkoutRelation::getProjFitnessChallengeId, challengeId)
                .eq(ProjFitnessChallengeManualWorkoutRelation::getDelFlag, GlobalConstant.NO)
                .orderByAsc(ProjFitnessChallengeManualWorkoutRelation::getId);

        return list(queryWrapper).stream()
                .map(ProjFitnessChallengeManualWorkoutRelation::getProjFitnessManualWorkoutId)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> getChallengeIdsByWorkoutId(Integer workoutId) {
        if (workoutId == null) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjFitnessChallengeManualWorkoutRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ProjFitnessChallengeManualWorkoutRelation::getProjFitnessChallengeId)
                .eq(ProjFitnessChallengeManualWorkoutRelation::getProjFitnessManualWorkoutId, workoutId)
                .eq(ProjFitnessChallengeManualWorkoutRelation::getDelFlag, GlobalConstant.NO);

        return list(queryWrapper).stream()
                .map(ProjFitnessChallengeManualWorkoutRelation::getProjFitnessChallengeId)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, List<Integer>> mapChallengeWorkouts(List<Integer> challengeIds) {
        if (CollUtil.isEmpty(challengeIds)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<ProjFitnessChallengeManualWorkoutRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(challengeIds), ProjFitnessChallengeManualWorkoutRelation::getProjFitnessChallengeId, challengeIds)
                .eq(ProjFitnessChallengeManualWorkoutRelation::getDelFlag, GlobalConstant.NO)
                .orderByAsc(ProjFitnessChallengeManualWorkoutRelation::getProjFitnessChallengeId)
                .orderByAsc(ProjFitnessChallengeManualWorkoutRelation::getId);

        List<ProjFitnessChallengeManualWorkoutRelation> relations = list(queryWrapper);
        return relations.stream()
                .collect(Collectors.groupingBy(
                        ProjFitnessChallengeManualWorkoutRelation::getProjFitnessChallengeId,
                        Collectors.mapping(
                                ProjFitnessChallengeManualWorkoutRelation::getProjFitnessManualWorkoutId,
                                Collectors.toList()
                        )
                ));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveChallengeWorkoutRelations(Integer challengeId, List<Integer> workoutIds, Integer projId) {
        if (challengeId == null || CollUtil.isEmpty(workoutIds) || projId == null) {
            log.warn("Invalid parameters for saving challenge workout relations: challengeId={}, workoutIds={}, projId={}",
                    challengeId, workoutIds, projId);
            return;
        }

        List<ProjFitnessChallengeManualWorkoutRelation> relations = new ArrayList<>();
        for (Integer workoutId : workoutIds) {
            ProjFitnessChallengeManualWorkoutRelation relation = new ProjFitnessChallengeManualWorkoutRelation();
            relation.setProjFitnessChallengeId(challengeId);
            relation.setProjFitnessManualWorkoutId(workoutId);
            relation.setProjId(projId);
            relations.add(relation);
        }

        saveBatch(relations);
        log.info("Saved {} challenge workout relations for challengeId: {}", relations.size(), challengeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateChallengeWorkoutRelations(Integer challengeId, List<Integer> workoutIds, Integer projId) {
        if (challengeId == null || projId == null) {
            log.warn("Invalid parameters for updating challenge workout relations: challengeId={}, projId={}",
                    challengeId, projId);
            return;
        }

        // 删除现有关联
        deleteByChallengeId(challengeId);

        // 保存新关联
        if (CollUtil.isNotEmpty(workoutIds)) {
            saveChallengeWorkoutRelations(challengeId, workoutIds, projId);
        }

        log.info("Updated challenge workout relations for challengeId: {}, new count: {}",
                challengeId, workoutIds != null ? workoutIds.size() : GlobalConstant.ZERO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByChallengeId(Integer challengeId) {
        if (challengeId == null) {
            log.warn("Challenge ID is null, skip delete");
            return;
        }

        LambdaUpdateWrapper<ProjFitnessChallengeManualWorkoutRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessChallengeManualWorkoutRelation::getDelFlag, GlobalConstant.YES)
                .eq(ProjFitnessChallengeManualWorkoutRelation::getProjFitnessChallengeId, challengeId);

        update(new ProjFitnessChallengeManualWorkoutRelation(), updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByChallengeIds(List<Integer> challengeIds) {
        if (CollUtil.isEmpty(challengeIds)) {
            log.warn("Challenge IDs list is empty, skip delete");
            return;
        }

        LambdaUpdateWrapper<ProjFitnessChallengeManualWorkoutRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessChallengeManualWorkoutRelation::getDelFlag, GlobalConstant.YES)
                .in(ProjFitnessChallengeManualWorkoutRelation::getProjFitnessChallengeId, challengeIds);

        update(new ProjFitnessChallengeManualWorkoutRelation(), updateWrapper);
    }
}
